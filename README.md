# E2E Tests for Clinic Portal Web Application

[![E2E Tests](https://github.com/highfive-vet/e2e-tests/actions/workflows/e2e-tests.yml/badge.svg)](https://github.com/highfive-vet/e2e-tests/actions/workflows/e2e-tests.yml)

📊 **[View Latest Test Report](https://highfive-vet.github.io/e2e-tests/)**

This directory contains end-to-end tests for the clinic portal web application using Playwright.

## Setup

The tests are already configured and ready to run. Playwright browsers are installed globally.

## Running Tests

### From the root directory:

```bash
# Run all e2e tests
npm run e2e

# Run tests with browser UI visible
npm run e2e:headed

# Run tests with Playwright UI mode
npm run e2e:ui

# Debug tests step by step
npm run e2e:debug

# View test report
npm run e2e:report
```

### From the e2e-tests directory:

```bash
cd e2e-tests

# Run all tests
npm test

# Run tests with browser UI visible
npm run test:headed

# Run tests with Playwright UI mode
npm run test:ui

# Debug tests
npm run test:debug

# Run tests on specific browsers
npm run test:chromium
npm run test:firefox
npm run test:webkit
npm run test:all

# Run tests on specific environments
npm run test:staging
npm run test:production

# Run tests with local script (interactive)
npm run test:local

# View test report
npm run report
```

## Test Structure

- `tests/` - Contains all test files
- `playwright.config.ts` - Playwright configuration
- `package.json` - Dependencies and scripts

## Configuration

The tests are configured to:
- Run against `https://staging.app.highfive.vet` by default
- Test on Chromium, Firefox, and WebKit browsers
- Include mobile viewport testing
- Take screenshots and videos on failure
- Generate HTML reports

## GitHub Actions

This project includes a GitHub Action workflow for running e2e tests manually:

### Manual Trigger
1. Go to the **Actions** tab in your GitHub repository
2. Select **E2E Tests** from the workflow list
3. Click **Run workflow**
4. Configure options:
   - **Environment**: `staging` or `production`
   - **Browser**: `chromium`, `firefox`, `webkit`, or `all`
   - **Headed**: Run with visible browser (for debugging)
   - **Debug**: Enable debug mode

### Workflow Features
- ✅ Manual trigger with configurable options
- 🌐 Chrome browser testing on staging environment
- 📊 Automatic test report generation
- 📁 Test artifacts upload (reports, screenshots)
- 🌐 **Live test reports hosted on GitHub Pages**
- ⏱️ 60-minute timeout protection

### 📊 Test Reports
- **Live Report**: [https://highfive-vet.github.io/e2e-tests/](https://highfive-vet.github.io/e2e-tests/)
- **Updated automatically** after each workflow run
- **Interactive Playwright report** with screenshots, traces, and detailed test results

See [.github/workflows/README.md](.github/workflows/README.md) for detailed documentation.

## Writing Tests

Test files should be placed in the `tests/` directory and follow the naming convention `*.spec.ts`.

Example test structure:
```typescript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test('should do something', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/Expected Title/);
  });
});
```

## Best Practices

1. Use descriptive test names
2. Group related tests with `test.describe()`
3. Use page object models for complex interactions
4. Wait for elements to be visible before interacting
5. Use `page.waitForLoadState('networkidle')` for dynamic content
6. Avoid hard-coded waits (`page.waitForTimeout()`)

{"name": "e2e-tests", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:all": "playwright test --project=chromium --project=firefox --project=webkit", "test:staging": "BASE_URL=https://staging.app.highfive.vet playwright test", "test:production": "BASE_URL=https://app.highfive.vet playwright test", "test:local": "./scripts/run-tests.sh", "report": "playwright show-report", "install": "playwright install"}, "keywords": ["e2e", "testing", "playwright", "clinic-portal"], "author": "", "license": "ISC", "description": "End-to-end tests for the clinic portal web application", "dependencies": {"@playwright/test": "^1.55.1"}}
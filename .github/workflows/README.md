# GitHub Actions Workflows

## E2E Tests Workflow

This workflow runs end-to-end tests using Playwright and can be triggered manually.

### Manual Trigger

1. Go to the **Actions** tab in your GitHub repository
2. Select **E2E Tests** from the workflow list
3. Click **Run workflow**
4. Configure the following options:

#### Workflow Inputs

- **Environment**: Choose which environment to test against
  - `staging` (default): Tests against https://staging.app.highfive.vet
  - `production`: Tests against https://app.highfive.vet

- **Browser**: Select which browser(s) to run tests on
  - `chromium` (default): Run tests on Chromium
  - `firefox`: Run tests on Firefox
  - `webkit`: Run tests on WebKit (Safari)
  - `all`: Run tests on all browsers

- **Headed**: Run tests in headed mode (visible browser)
  - `false` (default): Run tests headlessly
  - `true`: Run tests with visible browser (useful for debugging)

- **Debug**: Run tests in debug mode
  - `false` (default): Normal test execution
  - `true`: Run tests in debug mode with additional logging

### Artifacts

The workflow automatically uploads the following artifacts:

1. **Playwright Report**: HTML report with detailed test results
2. **Test Results**: Raw test result files and screenshots

Artifacts are retained for 30 days and can be downloaded from the workflow run page.

### Example Usage

#### Basic Test Run
- Environment: `staging`
- Browser: `chromium`
- Headed: `false`
- Debug: `false`

#### Debug Session
- Environment: `staging`
- Browser: `chromium`
- Headed: `true`
- Debug: `true`

#### Cross-browser Testing
- Environment: `staging`
- Browser: `all`
- Headed: `false`
- Debug: `false`

#### Production Validation
- Environment: `production`
- Browser: `chromium`
- Headed: `false`
- Debug: `false`

### Local Development

To run tests locally:

```bash
# Install dependencies
npm install

# Install Playwright browsers
npx playwright install

# Run tests
npm test

# Run tests in headed mode
npm run test:headed

# Run tests with UI
npm run test:ui

# Run tests in debug mode
npm run test:debug

# View test report
npm run report
```

### Environment Variables

The workflow sets the following environment variables:

- `BASE_URL`: The base URL for the application being tested
- `CI`: Set to `true` to enable CI-specific configurations

### Browser Configuration

The Playwright configuration supports multiple browsers:

- **Chromium**: Desktop Chrome simulation
- **Firefox**: Desktop Firefox simulation  
- **WebKit**: Desktop Safari simulation
- **Staging**: Legacy configuration for backward compatibility

Each browser project uses the `BASE_URL` environment variable or defaults to the staging environment.

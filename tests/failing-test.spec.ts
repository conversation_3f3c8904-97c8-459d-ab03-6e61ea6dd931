import { test, expect } from '@playwright/test';

test.describe('Failing Test Example', () => {
  test('should intentionally fail to test CI behavior', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Wait for redirect to login
    await expect(page).toHaveURL(/.*\/login/);

    // This assertion will fail intentionally
    await expect(page).toHaveTitle('This Title Does Not Exist');
  });

  test('should fail with element not found', async ({ page }) => {
    await page.goto('/');
    
    // Wait for redirect to login
    await expect(page).toHaveURL(/.*\/login/);

    // This will fail because this element doesn't exist
    const nonExistentElement = page.locator('#this-element-does-not-exist');
    await expect(nonExistentElement).toBeVisible();
  });

  test('should fail with wrong text content', async ({ page }) => {
    await page.goto('/');
    
    // Wait for redirect to login
    await expect(page).toHaveURL(/.*\/login/);

    // Find an actual element but expect wrong text
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    await expect(emailInput).toHaveText('This text will never match an input field');
  });
});

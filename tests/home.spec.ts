import { test, expect } from '@playwright/test';

test.describe('Clinic Portal Home Page', () => {
  test('should load the application and redirect to login', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Since the Home component redirects unauthenticated users to login,
    // we should end up on the login page
    await expect(page).toHaveURL(/.*\/login/);

    // Check that the page loads successfully
    await expect(page).toHaveTitle(/Highfive/);

    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');

    // Check for key UI elements on the login page
    // Look for email input field
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    await expect(emailInput).toBeVisible();

    // Look for password input field
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    await expect(passwordInput).toBeVisible();

    // Look for login button
    const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign In"), button[type="submit"]');
    await expect(loginButton).toBeVisible();
  });

  test('should have proper page structure', async ({ page }) => {
    await page.goto('/');

    // Wait for redirect to login
    await expect(page).toHaveURL(/.*\/login/);

    // Check that the page has proper HTML structure
    await expect(page.locator('html')).toBeVisible();
    await expect(page.locator('body')).toBeVisible();

    // Check for React root element if applicable (e.g., <div id="root">)
    const rootElement = page.locator('#root');
    await expect(rootElement).toBeVisible();
  });

  test('should not have critical console errors on page load', async ({ page }) => {
    const consoleErrors: string[] = [];

    // Listen for console errors
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/');

    // Wait for redirect and page load
    await expect(page).toHaveURL(/.*\/login/);
    await page.waitForLoadState('networkidle');

    // Check that there are no critical console errors
    // Filter out expected/non-critical errors for unauthenticated users
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_') &&
      !error.includes('401') &&
      !error.includes('Unauthorized') &&
      !error.includes('Failed to load resource: the server responded with a status of 401')
    );

    expect(criticalErrors).toHaveLength(0);
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/');
    await expect(page).toHaveURL(/.*\/login/);

    // Check that the page is still functional on mobile
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    await expect(emailInput).toBeVisible();

    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    await expect(passwordInput).toBeVisible();
  });
});

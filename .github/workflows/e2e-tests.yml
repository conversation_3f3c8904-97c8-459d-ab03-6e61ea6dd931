name: E2E Tests

on:
  # Allow manual triggering of the workflow
  workflow_dispatch:
    inputs:
      headed:
        description: 'Run tests in headed mode (visible browser)'
        required: false
        default: false
        type: boolean
      debug:
        description: 'Run tests in debug mode'
        required: false
        default: false
        type: boolean

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps

    - name: Run Playwright tests
      run: |
        if [ "${{ github.event.inputs.headed }}" = "true" ]; then
          npx playwright test --headed
        elif [ "${{ github.event.inputs.debug }}" = "true" ]; then
          npx playwright test --debug
        else
          npx playwright test
        fi
      env:
        CI: true

    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: test-results/
        retention-days: 30

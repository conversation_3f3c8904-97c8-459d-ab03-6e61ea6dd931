name: E2E Tests

on:
  # Allow manual triggering of the workflow
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Get installed Playwright version
      id: playwright-version
      run: echo "PLAYWRIGHT_VERSION=$(node -e "console.log(require('./package-lock.json').packages['node_modules/@playwright/test'].version)")" >> $GITHUB_OUTPUT

    - name: Cache Playwright browsers
      uses: actions/cache@v4
      id: playwright-cache
      with:
        path: |
          ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.PLAYWRIGHT_VERSION }}

    - name: Install Playwright Browsers
      run: npx playwright install chromium --with-deps
      if: steps.playwright-cache.outputs.cache-hit != 'true'

    - name: Run Playwright tests
      run: npx playwright test
      env:
        CI: true

    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: test-results/
        retention-days: 30

    - name: Setup Pages
      uses: actions/configure-pages@v4
      if: always()

    - name: Upload to GitHub Pages
      uses: actions/upload-pages-artifact@v3
      if: always()
      with:
        path: ./playwright-report

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
      if: always()

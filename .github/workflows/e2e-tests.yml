name: E2E Tests

on:
  # Allow manual triggering of the workflow
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test against'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      browser:
        description: 'Browser to run tests on'
        required: true
        default: 'chromium'
        type: choice
        options:
          - chromium
          - firefox
          - webkit
          - all
      headed:
        description: 'Run tests in headed mode (visible browser)'
        required: false
        default: false
        type: boolean
      debug:
        description: 'Run tests in debug mode'
        required: false
        default: false
        type: boolean

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps

    - name: Set environment variables
      run: |
        if [ "${{ github.event.inputs.environment }}" = "production" ]; then
          echo "BASE_URL=https://app.highfive.vet" >> $GITHUB_ENV
        else
          echo "BASE_URL=https://staging.app.highfive.vet" >> $GITHUB_ENV
        fi

    - name: Run Playwright tests (All browsers)
      if: ${{ github.event.inputs.browser == 'all' }}
      run: npx playwright test --project=chromium --project=firefox --project=webkit
      env:
        CI: true

    - name: Run Playwright tests (Single browser)
      if: ${{ github.event.inputs.browser != 'all' }}
      run: |
        if [ "${{ github.event.inputs.headed }}" = "true" ]; then
          npx playwright test --project=${{ github.event.inputs.browser }} --headed
        elif [ "${{ github.event.inputs.debug }}" = "true" ]; then
          npx playwright test --project=${{ github.event.inputs.browser }} --debug
        else
          npx playwright test --project=${{ github.event.inputs.browser }}
        fi
      env:
        CI: true

    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ github.event.inputs.browser }}-${{ github.event.inputs.environment }}
        path: playwright-report/
        retention-days: 30

    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ github.event.inputs.browser }}-${{ github.event.inputs.environment }}
        path: test-results/
        retention-days: 30
